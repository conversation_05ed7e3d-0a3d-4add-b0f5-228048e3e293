#include <bits/stdc++.h>
using namespace std;
#define int long long
#define mod 998244353
#define pb push_back
const int N = 1e6 + 5;

template <typename T>
ostream &operator<<(ostream &os, const vector<T> &v)
{
  os << "[";
  for (size_t i = 0; i < v.size(); ++i)
  {
    if (i > 0)
      os << ", ";
    os << v[i];
  }
  os << "]";
  return os;
}

template <typename T>
ostream &operator<<(ostream &os, const set<T> &s)
{
  os << "{";
  for (auto it = s.begin(); it != s.end(); ++it)
  {
    if (it != s.begin())
      os << ", ";
    os << *it;
  }
  os << "}";
  return os;
}

template <typename K, typename V>
ostream &operator<<(ostream &os, const map<K, V> &m)
{
  os << "{";
  for (auto it = m.begin(); it != m.end(); ++it)
  {
    if (it != m.begin())
      os << ", ";
    os << it->first << ": " << it->second;
  }
  os << "}";
  return os;
}

#define debug(args...)                       \
  {                                          \
    string _s = #args;                       \
    replace(_s.begin(), _s.end(), ',', ' '); \
    stringstream _ss(_s);                    \
    istream_iterator<string> _it(_ss);       \
    err(_it, args);                          \
    cout << '\n';                            \
  }
void err(istream_iterator<string> it) {}
template <typename T, typename... Args>
void err(istream_iterator<string> it, T a, Args... args)
{
  cout << *it << " = " << a << ' ';
  err(++it, args...);
}

bool Mbe;

bool Men;

void solve()
{
  // cout << (&Mbe-&Men)/1048576.0 << "MB" << "\n";
  int n;
  cin >> n;
  int cnt1 = 0, cnt2 = 0;
  for (int i = 0; i < n; i++)
  {
    int a;
    cin >> a;
    if (a & 1)
      cnt2++;
    else
      cnt1++;
  }
  int ans = (cnt1 % mod) * (cnt2 % mod) % mod;
  cout << ans << "\n";
  // cout << 1e3*clock()/CLOCKS_PER_SEC << "ms" << "\n";
}

signed main()
{
  ios_base::sync_with_stdio(false);
  cin.tie(0);
  int t = 1;
  // cin >> t;
  while (t--)
  {
    solve();
  }
  return 0;
}