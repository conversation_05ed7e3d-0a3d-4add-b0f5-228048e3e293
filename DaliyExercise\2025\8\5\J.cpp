#include <bits/stdc++.h>
using namespace std;
using u64 = unsigned long long;
const u64 M = 998244353;
inline u64 mul_mod(u64 a, u64 b)
{
  return (u64)((__int128)a * b % M);
}
inline u64 ksm(u64 a, u64 b)
{
  u64 ret = 1;
  a %= M, b = (b % (M - 1) == 0) ? (M - 1) : (b % (M - 1));
  for (; b; b >>= 1)
  {
    if (b & 1)
      ret = mul_mod(ret, a);
    a = mul_mod(a, a);
  }
  return ret;
}
inline u64 gcd(u64 a, u64 b)
{
  for (u64 t; b;)
  {
    t = a % b, a = b, b = t;
  }
  return a;
}
void HelloMegu()
{
  u64 a, b, c, d;
  cin >> a >> b >> c >> d;
  u64 ans = 1, t;
  while (1)
  {
    if (b > d)
    {
      swap(a, c), swap(b, d);
    }
    t = gcd(a, c);
    if (t == 1 || b == 0 || d == 0)
      break;
    ans = mul_mod(ans, ksm(t, b));
    d -= b, c = t, a /= t;
  }
  printf("%llu\n", ans);
}
int main()
{
  std::ios::sync_with_stdio(false);
  std::cin.tie(0);
  std::cout.tie(0);
  int _ = 1;
  cin >> _;
  while (_--)
    HelloMegu();
  return 0;
}
