#include <bits/stdc++.h>
using namespace std;
using ll = long long;

// 求 1…N 的平方自由部分 f[i]
vector<int> calc_squarefree(int N)
{
  vector<int> f(N + 1);
  // 初始化 f[i]=i
  for (int i = 1; i <= N; i++)
    f[i] = i;
  // 枚举质数
  vector<bool> is_prime(N + 1, true);
  for (int p = 2; p <= N; p++)
  {
    if (!is_prime[p])
      continue;
    // 将所有 p^2 的因子从 f[i] 中去掉
    ll p2 = 1LL * p * p;
    for (ll j = p2; j <= N; j += p2)
    {
      while (f[j] % p2 == 0)
      {
        f[j] /= p2;
      }
    }
    // 标记合数
    for (int j = p * 2; j <= N; j += p)
      is_prime[j] = false;
  }
  return f;
}

int main()
{
  ios::sync_with_stdio(false);
  cin.tie(nullptr);

  int T;
  cin >> T;
  int maxn = 0;
  vector<int> Ns(T);
  for (int i = 0; i < T; i++)
  {
    cin >> Ns[i];
    maxn = max(maxn, Ns[i]);
  }

  // 预处理到 maxn
  auto f = calc_squarefree(maxn);

  // 预先生成质数列表（用于后补）
  vector<bool> is_prime(maxn + 1, true);
  vector<int> primes;
  is_prime[0] = is_prime[1] = false;
  for (int i = 2; i <= maxn; i++)
  {
    if (is_prime[i])
    {
      primes.push_back(i);
      for (int j = i * 2; j <= maxn; j += i)
        is_prime[j] = false;
    }
  }

  // 逐个测试
  for (int n : Ns)
  {
    int need = n / 2;
    vector<int> ans;
    double sq = sqrt(n);
    // 1) 首先选取 f[i] > sqrt(n) 的 i
    for (int i = 1; i <= n && (int)ans.size() < need; i++)
    {
      if (f[i] > sq)
      {
        ans.push_back(i);
      }
    }
    // 2) 不足则从最小质数开始补充
    for (int p : primes)
    {
      if ((int)ans.size() >= need)
        break;
      if (p > n)
        break;
      // 若尚未选入
      // 且其 f[p]=p <= sqrt(n)（因为 p 较小）也可以补充
      ans.push_back(p);
    }

    // 输出
    for (int x : ans)
      cout << x << ' ';
    cout << "\n";
  }
  return 0;
}
