#include <bits/stdc++.h>
using namespace std;
#define int long long
#define mod 998244353
#define pb push_back
const int N = 1e6 + 5;

template <typename T>
ostream &operator<<(ostream &os, const vector<T> &v)
{
  os << "[";
  for (size_t i = 0; i < v.size(); ++i)
  {
    if (i > 0)
      os << ", ";
    os << v[i];
  }
  os << "]";
  return os;
}

template <typename T>
ostream &operator<<(ostream &os, const set<T> &s)
{
  os << "{";
  for (auto it = s.begin(); it != s.end(); ++it)
  {
    if (it != s.begin())
      os << ", ";
    os << *it;
  }
  os << "}";
  return os;
}

template <typename K, typename V>
ostream &operator<<(ostream &os, const map<K, V> &m)
{
  os << "{";
  for (auto it = m.begin(); it != m.end(); ++it)
  {
    if (it != m.begin())
      os << ", ";
    os << it->first << ": " << it->second;
  }
  os << "}";
  return os;
}

#define debug(args...)                       \
  {                                          \
    string _s = #args;                       \
    replace(_s.begin(), _s.end(), ',', ' '); \
    stringstream _ss(_s);                    \
    istream_iterator<string> _it(_ss);       \
    err(_it, args);                          \
    cout << '\n';                            \
  }
void err(istream_iterator<string> it) {}
template <typename T, typename... Args>
void err(istream_iterator<string> it, T a, Args... args)
{
  cout << *it << " = " << a << ' ';
  err(++it, args...);
}

bool Mbe;
int prime[N];
vector<int> p;
bool Men;

void solve()
{
  // cout << (&Mbe-&Men)/1048576.0 << "MB" << "\n";
  int n;
  cin >> n;
  vector<int> vis(n + 1, 1), a(n + 1);
  int cnt = n / 2;
  vector<int> ans;
  for (int i = 1; i * i <= n; i++)
  {
    vis[i * i] = false;
  }
  for (int i = 0; i < p.size() && p[i] <= n && ans.size() < cnt; i++)
  {
    ans.push_back(p[i]);
    a[p[i]] = 1;
    for (int j = 0; j < i && p[j] * p[i] <= n; j++)
    {
      for (int k = 1; k * k * p[j] * p[i] <= n; k ++)
        vis[p[j] * p[i] * k * k] = false;
    }
  }
  for (int i = 1; i <= n && ans.size() < cnt; i++)
  {
    if (vis[i] && !a[i])
    {
      ans.push_back(i);
    }
  }
  sort(ans.begin(), ans.end());
  assert(ans.size() == n / 2);
  // cout << ans.size() << "\n";
  for (auto x : ans)
    cout << x << " ";
  cout << "\n";
  // cout << 1e3*clock()/CLOCKS_PER_SEC << "ms" << "\n";
}

void pre()
{
  for (int i = 2; i < N; i++)
  {
    if (!prime[i])
    {
      for (int j = i; j < N; j += i)
      {
        prime[j] = 1;
      }
      p.push_back(i);
    }
  }
}

signed main()
{
  ios_base::sync_with_stdio(false);
  cin.tie(0);
  int t = 1;
  cin >> t;
  pre();
  while (t--)
  {
    solve();
  }
  return 0;
}